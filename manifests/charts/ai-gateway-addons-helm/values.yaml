# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

# Default values for ai-gateway-addons-helm.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# Grafana dashboard configuration
grafana:
  # Enable Grafana dashboard creation
  enabled: true
  # Namespace where Grafana is deployed
  namespace: monitoring
  # Dashboard configuration
  dashboard:
    # Enable AI Gateway dashboard
    enabled: true
    # Dashboard title
    title: "AI Gateway Metrics"
    # Dashboard tags
    tags:
      - "ai-gateway"
      - "envoy"
      - "llm"
      - "metrics"
    # Dashboard folder
    folder: "AI Gateway"
    # Refresh interval
    refresh: "30s"
    # Time range
    time:
      from: "now-1h"
      to: "now"
    # Dashboard annotations
    annotations:
      enabled: true
    # Dashboard variables/templating
    templating:
      enabled: true
      # Add variables for filtering
      variables:
        - name: "namespace"
          type: "query"
          query: "label_values(gen_ai_client_token_usage, namespace)"
          refresh: "on_time_range_change"
        - name: "model"
          type: "query"
          query: "label_values(gen_ai_client_token_usage{namespace=\"$namespace\"}, gen_ai_request_model)"
          refresh: "on_time_range_change"
        - name: "operation"
          type: "query"
          query: "label_values(gen_ai_client_token_usage{namespace=\"$namespace\"}, gen_ai_operation_name)"
          refresh: "on_time_range_change"

# Prometheus ServiceMonitor configuration
prometheus:
  # Enable Prometheus ServiceMonitor creation
  enabled: true
  # ServiceMonitor configuration
  serviceMonitor:
    # Enable ServiceMonitor
    enabled: true
    # Namespace where ServiceMonitor should be created
    namespace: monitoring
    # Labels to add to ServiceMonitor
    labels:
      app.kubernetes.io/name: ai-gateway
      app.kubernetes.io/component: monitoring
    # Selector for services to monitor
    selector:
      matchLabels:
        app.kubernetes.io/name: ai-gateway
    # Endpoints configuration
    endpoints:
      - port: metrics
        path: /metrics
        interval: 30s
        scrapeTimeout: 10s
    # Target namespaces to monitor
    namespaceSelector:
      matchNames:
        - ai-gateway-system

# Global configuration
global:
  # Image registry
  imageRegistry: ""
  # Image pull secrets
  imagePullSecrets: []

# Common labels to add to all resources
commonLabels:
  app.kubernetes.io/part-of: ai-gateway
  app.kubernetes.io/managed-by: helm

# Common annotations to add to all resources
commonAnnotations: {}

# Resource limits and requests
resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 50m
    memory: 64Mi

# Node selector
nodeSelector: {}

# Tolerations
tolerations: []

# Affinity
affinity: {}

# Security context
securityContext:
  runAsNonRoot: true
  runAsUser: 65534
  fsGroup: 65534

# Pod security context
podSecurityContext:
  runAsNonRoot: true
  runAsUser: 65534
  fsGroup: 65534

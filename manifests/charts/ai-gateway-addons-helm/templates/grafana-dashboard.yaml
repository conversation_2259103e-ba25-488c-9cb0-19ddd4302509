# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

{{- if and .Values.grafana.enabled .Values.grafana.dashboard.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "ai-gateway-addons.fullname" . }}-dashboard
  namespace: {{ .Values.grafana.namespace | default .Release.Namespace }}
  labels:
    {{- include "ai-gateway-addons.labels" . | nindent 4 }}
    grafana_dashboard: "1"
  annotations:
    {{- include "ai-gateway-addons.annotations" . | nindent 4 }}
data:
  ai-gateway-dashboard.json: |
    {
      "dashboard": {
        "id": null,
        "uid": "ai-gateway-metrics",
        "title": "{{ .Values.grafana.dashboard.title }}",
        "tags": {{ .Values.grafana.dashboard.tags | toJson }},
        "style": "dark",
        "timezone": "browser",
        "refresh": "{{ .Values.grafana.dashboard.refresh }}",
        "time": {
          "from": "{{ .Values.grafana.dashboard.time.from }}",
          "to": "{{ .Values.grafana.dashboard.time.to }}"
        },
        "templating": {
          "list": [
            {
              "name": "namespace",
              "type": "query",
              "query": "label_values(gen_ai_client_token_usage, namespace)",
              "refresh": 1,
              "includeAll": true,
              "allValue": ".*",
              "multi": true,
              "current": {
                "selected": false,
                "text": "All",
                "value": "$__all"
              }
            },
            {
              "name": "model",
              "type": "query",
              "query": "label_values(gen_ai_client_token_usage{namespace=~\"$namespace\"}, gen_ai_request_model)",
              "refresh": 1,
              "includeAll": true,
              "allValue": ".*",
              "multi": true,
              "current": {
                "selected": false,
                "text": "All",
                "value": "$__all"
              }
            },
            {
              "name": "operation",
              "type": "query",
              "query": "label_values(gen_ai_client_token_usage{namespace=~\"$namespace\"}, gen_ai_operation_name)",
              "refresh": 1,
              "includeAll": true,
              "allValue": ".*",
              "multi": true,
              "current": {
                "selected": false,
                "text": "All",
                "value": "$__all"
              }
            }
          ]
        },
        "panels": [
          {
            "id": 1,
            "title": "Token Usage Rate by Type",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(rate(gen_ai_client_token_usage_sum{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (gen_ai_token_type)",
                "legendFormat": "{{`{{ gen_ai_token_type }}`}}",
                "refId": "A"
              }
            ],
            "gridPos": {"h": 8, "w": 8, "x": 0, "y": 0},
            "fieldConfig": {
              "defaults": {
                "unit": "reqps",
                "color": {
                  "mode": "palette-classic"
                }
              }
            }
          },
          {
            "id": 2,
            "title": "Request Duration Percentiles",
            "type": "timeseries",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(gen_ai_server_request_duration_seconds_bucket{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (le))",
                "legendFormat": "95th percentile",
                "refId": "A"
              },
              {
                "expr": "histogram_quantile(0.50, sum(rate(gen_ai_server_request_duration_seconds_bucket{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (le))",
                "legendFormat": "50th percentile",
                "refId": "B"
              }
            ],
            "gridPos": {"h": 8, "w": 8, "x": 8, "y": 0},
            "fieldConfig": {
              "defaults": {
                "unit": "s"
              }
            }
          },
          {
            "id": 3,
            "title": "Time to First Token",
            "type": "timeseries",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(gen_ai_server_time_to_first_token_seconds_bucket{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (le))",
                "legendFormat": "95th percentile",
                "refId": "A"
              },
              {
                "expr": "histogram_quantile(0.50, sum(rate(gen_ai_server_time_to_first_token_seconds_bucket{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (le))",
                "legendFormat": "50th percentile",
                "refId": "B"
              }
            ],
            "gridPos": {"h": 8, "w": 8, "x": 16, "y": 0},
            "fieldConfig": {
              "defaults": {
                "unit": "s"
              }
            }
          },
          {
            "id": 4,
            "title": "Requests by Model",
            "type": "piechart",
            "targets": [
              {
                "expr": "sum(rate(gen_ai_client_token_usage_count{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (gen_ai_request_model)",
                "legendFormat": "{{`{{ gen_ai_request_model }}`}}",
                "refId": "A"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 5,
            "title": "Requests by Operation",
            "type": "piechart",
            "targets": [
              {
                "expr": "sum(rate(gen_ai_client_token_usage_count{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (gen_ai_operation_name)",
                "legendFormat": "{{`{{ gen_ai_operation_name }}`}}",
                "refId": "A"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ]
      }
    }
{{- end }}

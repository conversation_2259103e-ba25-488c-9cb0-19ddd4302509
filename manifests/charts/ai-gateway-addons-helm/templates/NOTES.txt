Thank you for installing {{ .Chart.Name }}.

Your release is named {{ .Release.Name }}.

To learn more about the release, try:

  $ helm status {{ .Release.Name }}
  $ helm get values {{ .Release.Name }}

{{- if .Values.grafana.enabled }}

Grafana Dashboard:
{{- if .Values.grafana.dashboard.enabled }}
  A Grafana dashboard for AI Gateway metrics has been created as a ConfigMap.
  Dashboard name: {{ include "ai-gateway-addons.fullname" . }}-dashboard
  Namespace: {{ .Values.grafana.namespace | default .Release.Namespace }}

  The dashboard includes the following panels:
  - Token Usage Rate by Type
  - Request Duration Percentiles
  - Time to First Token
  - Requests by Model
  - Requests by Operation

  To access the dashboard, ensure <PERSON><PERSON> is configured to discover dashboards
  from ConfigMaps with the label "grafana_dashboard: 1".
{{- end }}
{{- end }}

{{- if .Values.prometheus.enabled }}

Prometheus ServiceMonitor:
{{- if .Values.prometheus.serviceMonitor.enabled }}
  A ServiceMonitor has been created to scrape AI Gateway metrics.
  ServiceMonitor name: {{ include "ai-gateway-addons.fullname" . }}
  Namespace: {{ .Values.prometheus.serviceMonitor.namespace | default .Release.Namespace }}

  The ServiceMonitor will discover services with the following labels:
  {{- range $key, $value := .Values.prometheus.serviceMonitor.selector.matchLabels }}
  - {{ $key }}: {{ $value }}
  {{- end }}

  Metrics will be scraped from the following namespaces:
  {{- range .Values.prometheus.serviceMonitor.namespaceSelector.matchNames }}
  - {{ . }}
  {{- end }}
{{- end }}
{{- end }}

For more information about AI Gateway monitoring, visit:
https://aigateway.envoyproxy.io/docs/capabilities/metrics

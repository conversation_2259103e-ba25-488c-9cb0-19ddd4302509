# Copyright Envoy AI Gateway Authors
# SPDX-License-Identifier: Apache-2.0
# The full text of the Apache license is available in the LICENSE file at
# the root of the repo.

{{- if and .Values.prometheus.enabled .Values.prometheus.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "ai-gateway-addons.fullname" . }}
  namespace: {{ .Values.prometheus.serviceMonitor.namespace | default .Release.Namespace }}
  labels:
    {{- include "ai-gateway-addons.labels" . | nindent 4 }}
    {{- with .Values.prometheus.serviceMonitor.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  annotations:
    {{- include "ai-gateway-addons.annotations" . | nindent 4 }}
spec:
  selector:
    {{- toYaml .Values.prometheus.serviceMonitor.selector | nindent 4 }}
  {{- with .Values.prometheus.serviceMonitor.namespaceSelector }}
  namespaceSelector:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  endpoints:
  {{- range .Values.prometheus.serviceMonitor.endpoints }}
  - port: {{ .port }}
    path: {{ .path | default "/metrics" }}
    interval: {{ .interval | default "30s" }}
    scrapeTimeout: {{ .scrapeTimeout | default "10s" }}
    {{- if .honorLabels }}
    honorLabels: {{ .honorLabels }}
    {{- end }}
    {{- if .metricRelabelings }}
    metricRelabelings:
    {{- toYaml .metricRelabelings | nindent 4 }}
    {{- end }}
    {{- if .relabelings }}
    relabelings:
    {{- toYaml .relabelings | nindent 4 }}
    {{- end }}
  {{- end }}
{{- end }}

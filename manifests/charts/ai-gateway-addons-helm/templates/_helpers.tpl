{{/*
Copyright Envoy AI Gateway Authors
SPDX-License-Identifier: Apache-2.0
The full text of the Apache license is available in the LICENSE file at
the root of the repo.
*/}}

{{/*
Expand the name of the chart.
*/}}
{{- define "ai-gateway-addons.name" -}}
{{- default .Chart.Name .Values.nameOverride | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Create a default fully qualified app name.
We truncate at 63 chars because some Kubernetes name fields are limited to this (by the DNS naming spec).
If release name contains chart name it will be used as a full name.
*/}}
{{- define "ai-gateway-addons.fullname" -}}
{{- if .Values.fullnameOverride }}
{{- .Values.fullnameOverride | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- $name := default .Chart.Name .Values.nameOverride }}
{{- if contains $name .Release.Name }}
{{- .Release.Name | trunc 63 | trimSuffix "-" }}
{{- else }}
{{- printf "%s-%s" .Release.Name $name | trunc 63 | trimSuffix "-" }}
{{- end }}
{{- end }}
{{- end }}

{{/*
Create chart name and version as used by the chart label.
*/}}
{{- define "ai-gateway-addons.chart" -}}
{{- printf "%s-%s" .Chart.Name .Chart.Version | replace "+" "_" | trunc 63 | trimSuffix "-" }}
{{- end }}

{{/*
Common labels
*/}}
{{- define "ai-gateway-addons.labels" -}}
helm.sh/chart: {{ include "ai-gateway-addons.chart" . }}
{{ include "ai-gateway-addons.selectorLabels" . }}
{{- if .Chart.AppVersion }}
app.kubernetes.io/version: {{ .Chart.AppVersion | quote }}
{{- end }}
app.kubernetes.io/managed-by: {{ .Release.Service }}
{{- with .Values.commonLabels }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Selector labels
*/}}
{{- define "ai-gateway-addons.selectorLabels" -}}
app.kubernetes.io/name: {{ include "ai-gateway-addons.name" . }}
app.kubernetes.io/instance: {{ .Release.Name }}
{{- end }}

{{/*
Common annotations
*/}}
{{- define "ai-gateway-addons.annotations" -}}
{{- with .Values.commonAnnotations }}
{{ toYaml . }}
{{- end }}
{{- end }}

{{/*
Create the name of the service account to use
*/}}
{{- define "ai-gateway-addons.serviceAccountName" -}}
{{- if .Values.serviceAccount.create }}
{{- default (include "ai-gateway-addons.fullname" .) .Values.serviceAccount.name }}
{{- else }}
{{- default "default" .Values.serviceAccount.name }}
{{- end }}
{{- end }}

{{/*
Generate Grafana dashboard JSON
*/}}
{{- define "ai-gateway-addons.grafanaDashboard" -}}
{
  "dashboard": {
    "id": null,
    "title": "{{ .Values.grafana.dashboard.title }}",
    "tags": {{ .Values.grafana.dashboard.tags | toJson }},
    "style": "dark",
    "timezone": "browser",
    "refresh": "{{ .Values.grafana.dashboard.refresh }}",
    "time": {
      "from": "{{ .Values.grafana.dashboard.time.from }}",
      "to": "{{ .Values.grafana.dashboard.time.to }}"
    },
    "templating": {
      "list": [
        {
          "name": "namespace",
          "type": "query",
          "query": "label_values(gen_ai_client_token_usage, namespace)",
          "refresh": 1,
          "includeAll": true,
          "allValue": ".*",
          "multi": true
        },
        {
          "name": "model",
          "type": "query",
          "query": "label_values(gen_ai_client_token_usage{namespace=~\"$namespace\"}, gen_ai_request_model)",
          "refresh": 1,
          "includeAll": true,
          "allValue": ".*",
          "multi": true
        },
        {
          "name": "operation",
          "type": "query",
          "query": "label_values(gen_ai_client_token_usage{namespace=~\"$namespace\"}, gen_ai_operation_name)",
          "refresh": 1,
          "includeAll": true,
          "allValue": ".*",
          "multi": true
        }
      ]
    },
    "panels": [
      {
        "id": 1,
        "title": "Token Usage by Type",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(rate(gen_ai_client_token_usage_sum{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (gen_ai_token_type)",
            "legendFormat": "{{`{{ gen_ai_token_type }}`}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Request Duration",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, sum(rate(gen_ai_server_request_duration_seconds_bucket{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (le))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, sum(rate(gen_ai_server_request_duration_seconds_bucket{namespace=~\"$namespace\",gen_ai_request_model=~\"$model\",gen_ai_operation_name=~\"$operation\"}[5m])) by (le))",
            "legendFormat": "50th percentile"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      }
    ]
  }
}
{{- end }}

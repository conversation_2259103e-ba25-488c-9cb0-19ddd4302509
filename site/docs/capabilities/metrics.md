---
id: metrics
title: AI/LLM Metrics
sidebar_position: 6
---

When using the Envoy AI Gateway, it will collect AI specific metrics and expose them to Prometheus for monitoring by default.
This guide provides an overview of the metrics collected by the AI Gateway and how to monitor them using Prometheus.

## Overview

Envoy AI Gateway is designed to intercept and process AI/LLM requests, that enables it to collect metrics for monitoring and observability.
Currently, it collects metrics and exports them to Prometheus for monitoring in the OpenTelemetry format as specified by the [OpenTelemetry Gen AI Semantic Conventions](https://opentelemetry.io/docs/specs/semconv/attributes-registry/gen-ai/).
Not all metrics are supported yet, but the Envoy AI Gateway will continue to add more metrics in the future.

For example, the Envoy AI Gateway collects metrics such as:
* [**`gen_ai.client.token.usage`**](https://opentelemetry.io/docs/specs/semconv/gen-ai/gen-ai-metrics/#metric-gen_aiclienttokenusage): Number of tokens processed. The label `gen_ai_token_type` can be used to differentiate between input, output, and total tokens.
* [**`gen_ai.server.request.duration`**](https://opentelemetry.io/docs/specs/semconv/gen-ai/gen-ai-metrics/#metric-gen_aiserverrequestduration): Measured from the start of the received request headers in the Envoy AI Gateway filter to the end of the processed response body processing.
* [**`gen_ai.server.time_to_first_token`**](https://opentelemetry.io/docs/specs/semconv/gen-ai/gen-ai-metrics/#metric-gen_aiservertime_to_first_token): Measured from the start of the received request headers in the Envoy AI Gateway filter to the receiving of the first token in the response body handling.
* [**`gen_ai.server.time_per_output_token`**](https://opentelemetry.io/docs/specs/semconv/gen-ai/gen-ai-metrics/#metric-gen_aiservertime_per_output_token): The latency between consecutive tokens, if supported, or by chunks/tokens otherwise.

Each metric comes with some default labels such as `gen_ai_request_model` that contains the model name, etc.

## Trying it out

Before you begin, you'll need to complete the basic setup from the [Basic Usage](../getting-started/basic-usage.md) guide.

Then, you can install the prometheus using the following commands:

```shell
kubectl apply -f https://raw.githubusercontent.com/envoyproxy/ai-gateway/main/examples/monitoring/monitoring.yaml
```

Let's wait for a while until the Prometheus is up and running.
```shell
kubectl wait --for=condition=ready pod -l app=prometheus -n monitoring
```

To access the Prometheus dashboard, you need to port-forward the Prometheus service to your local machine like this:

```shell
kubectl port-forward -n monitoring svc/prometheus 9090:9090
```

Now open your browser and navigate to `http://localhost:9090` to access the Prometheus dashboard to explore the metrics.

Alternatively, you can make the following requests to see the raw metrics:

```shell
curl http://localhost:9090/api/v1/query --data-urlencode \
  'query=sum(gen_ai_client_token_usage_sum{gateway_envoyproxy_io_owning_gateway_name = "envoy-ai-gateway-basic"}) by (gen_ai_request_model, gen_ai_token_type)' \
    | jq '.data.result[]'
```

and then you would get the response like this, assuming you have made some requests with the model `gpt-4o-mini`:

```json lines
{
  "metric": {
    "gen_ai_request_model": "gpt-4o-mini",
    "gen_ai_token_type": "input"
  },
  "value": [
    1743105857.684,
    "12"
  ]
}
{
  "metric": {
    "gen_ai_request_model": "gpt-4o-mini",
    "gen_ai_token_type": "output"
  },
  "value": [
    1743105857.684,
    "13"
  ]
}
{
  "metric": {
    "gen_ai_request_model": "gpt-4o-mini",
    "gen_ai_token_type": "total"
  },
  "value": [
    1743105857.684,
    "25"
  ]
}
```

## Dashboard and Monitoring Setup

The AI Gateway provides a comprehensive monitoring solution through Helm charts that include Grafana dashboards and Prometheus ServiceMonitors.

### Installing the Monitoring Addons

You can install the AI Gateway monitoring addons using the dedicated Helm chart:

```shell
# Add the AI Gateway Helm repository (if not already added)
helm repo add ai-gateway oci://docker.io/envoyproxy
helm repo update

# Install the monitoring addons
helm install ai-gateway-addons ai-gateway/ai-gateway-addons-helm \
  --namespace monitoring \
  --create-namespace \
  --set grafana.enabled=true \
  --set prometheus.enabled=true
```

### Grafana Dashboard

The monitoring addons include a pre-configured Grafana dashboard that visualizes AI Gateway metrics. The dashboard includes:

* **Token Usage Rate by Type**: Shows the rate of input, output, and total tokens processed
* **Request Duration Percentiles**: Displays 95th and 50th percentile request latencies
* **Time to First Token**: Shows latency metrics for the first token in streaming responses
* **Requests by Model**: Pie chart showing distribution of requests across different AI models
* **Requests by Operation**: Pie chart showing distribution between chat and embedding operations

The dashboard supports filtering by:
* **Namespace**: Filter metrics by Kubernetes namespace
* **Model**: Filter by specific AI model (e.g., gpt-4, claude-3, etc.)
* **Operation**: Filter by operation type (chat, embedding)

### Prometheus ServiceMonitor

The chart also creates a ServiceMonitor resource that automatically configures Prometheus to scrape AI Gateway metrics. The ServiceMonitor:

* Discovers AI Gateway services with the appropriate labels
* Scrapes metrics from the `/metrics` endpoint
* Applies proper relabeling for consistent metric naming
* Supports multiple namespaces for metric collection

### Configuration Options

You can customize the monitoring setup through Helm values:

```yaml
# Grafana configuration
grafana:
  enabled: true
  namespace: monitoring
  dashboard:
    enabled: true
    title: "AI Gateway Metrics"
    refresh: "30s"

# Prometheus configuration
prometheus:
  enabled: true
  serviceMonitor:
    enabled: true
    namespace: monitoring
    selector:
      matchLabels:
        app.kubernetes.io/name: ai-gateway
    namespaceSelector:
      matchNames:
        - ai-gateway-system
```

### Accessing the Dashboard

Once installed, the Grafana dashboard will be automatically discovered by Grafana instances configured to watch for ConfigMaps with the `grafana_dashboard: "1"` label.

To access the dashboard:

1. Open your Grafana instance
2. Navigate to "Dashboards"
3. Look for "AI Gateway Metrics" in the "AI Gateway" folder
4. The dashboard will display real-time metrics from your AI Gateway deployment

For more advanced monitoring setups and custom dashboard configurations, refer to the [Helm chart documentation](https://github.com/envoyproxy/ai-gateway/tree/main/manifests/charts/ai-gateway-addons-helm).

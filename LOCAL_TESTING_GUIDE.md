# AI Gateway Addons Chart 本地测试指南

本指南将帮助你在本地环境中测试 AI Gateway Addons Helm Chart。

## 前置条件

1. **Kubernetes 集群**（选择其中一种）：
   - Docker Desktop with Kubernetes
   - Minikube
   - Kind
   - K3s

2. **必需工具**：
   - `helm` (v3.x)
   - `kubectl`

## 测试步骤

### 1. 基础模板测试

首先验证 Helm 模板是否正确渲染：

```bash
# 基本模板渲染测试
helm template test-addons ./manifests/charts/ai-gateway-addons-helm

# 带调试信息的模板测试
helm template test-addons ./manifests/charts/ai-gateway-addons-helm --debug

# 测试不同配置
helm template test-addons ./manifests/charts/ai-gateway-addons-helm \
  --set grafana.enabled=false \
  --set prometheus.enabled=true

# 测试自定义值
helm template test-addons ./manifests/charts/ai-gateway-addons-helm \
  --set grafana.dashboard.title="My Custom Dashboard" \
  --set prometheus.serviceMonitor.namespace="custom-monitoring"
```

### 2. Chart 验证测试

```bash
# Lint 检查
helm lint ./manifests/charts/ai-gateway-addons-helm

# 使用项目的 Makefile 进行完整测试
make helm-test
```

### 3. 本地 Kubernetes 集群测试

#### 3.1 准备测试环境

```bash
# 创建测试命名空间
kubectl create namespace ai-gateway-system
kubectl create namespace monitoring

# 创建一个模拟的 AI Gateway Service（用于 ServiceMonitor 测试）
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: ai-gateway-test
  namespace: ai-gateway-system
  labels:
    app.kubernetes.io/name: ai-gateway
spec:
  ports:
  - name: metrics
    port: 8080
    targetPort: 8080
  selector:
    app: ai-gateway-test
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-gateway-test
  namespace: ai-gateway-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ai-gateway-test
  template:
    metadata:
      labels:
        app: ai-gateway-test
    spec:
      containers:
      - name: test
        image: nginx:alpine
        ports:
        - containerPort: 8080
EOF
```

#### 3.2 安装 Chart

```bash
# 安装 addons chart
helm install ai-gateway-addons ./manifests/charts/ai-gateway-addons-helm \
  --namespace monitoring \
  --set grafana.enabled=true \
  --set prometheus.enabled=true

# 查看安装状态
helm status ai-gateway-addons -n monitoring

# 查看创建的资源
kubectl get configmap -n monitoring -l grafana_dashboard=1
kubectl get servicemonitor -n monitoring
```

#### 3.3 验证资源

```bash
# 检查 Grafana Dashboard ConfigMap
kubectl get configmap -n monitoring -o yaml | grep -A 10 "ai-gateway-dashboard.json"

# 检查 ServiceMonitor
kubectl describe servicemonitor ai-gateway-addons-ai-gateway-addons-helm -n monitoring

# 验证 ServiceMonitor 的选择器
kubectl get services -n ai-gateway-system -l app.kubernetes.io/name=ai-gateway
```

### 4. 功能测试

#### 4.1 测试 Grafana Dashboard

如果你有 Grafana 运行：

```bash
# 部署一个简单的 Grafana 实例进行测试
helm repo add grafana https://grafana.github.io/helm-charts
helm install grafana grafana/grafana \
  --namespace monitoring \
  --set sidecar.dashboards.enabled=true \
  --set sidecar.dashboards.label=grafana_dashboard

# 获取 Grafana admin 密码
kubectl get secret --namespace monitoring grafana -o jsonpath="{.data.admin-password}" | base64 --decode

# 端口转发访问 Grafana
kubectl port-forward --namespace monitoring svc/grafana 3000:80
```

然后访问 http://localhost:3000，使用 admin 用户登录，查看是否有 "AI Gateway Metrics" dashboard。

#### 4.2 测试 Prometheus ServiceMonitor

如果你有 Prometheus Operator：

```bash
# 安装 Prometheus Operator
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace monitoring

# 检查 ServiceMonitor 是否被 Prometheus 发现
kubectl port-forward --namespace monitoring svc/prometheus-kube-prometheus-prometheus 9090:9090
```

访问 http://localhost:9090，在 Status -> Targets 中查看是否有 ai-gateway 相关的 targets。

### 5. 清理测试环境

```bash
# 卸载 chart
helm uninstall ai-gateway-addons -n monitoring

# 清理测试资源
kubectl delete namespace ai-gateway-system
kubectl delete namespace monitoring

# 或者只删除测试用的 deployment 和 service
kubectl delete deployment ai-gateway-test -n ai-gateway-system
kubectl delete service ai-gateway-test -n ai-gateway-system
```

### 6. 高级测试场景

#### 6.1 测试不同配置组合

```bash
# 只启用 Grafana
helm template test-grafana ./manifests/charts/ai-gateway-addons-helm \
  --set grafana.enabled=true \
  --set prometheus.enabled=false

# 只启用 Prometheus
helm template test-prometheus ./manifests/charts/ai-gateway-addons-helm \
  --set grafana.enabled=false \
  --set prometheus.enabled=true

# 自定义配置
helm template test-custom ./manifests/charts/ai-gateway-addons-helm \
  --set grafana.dashboard.title="Custom AI Gateway Dashboard" \
  --set grafana.namespace="custom-grafana" \
  --set prometheus.serviceMonitor.namespace="custom-prometheus"
```

#### 6.2 验证 JSON Dashboard 格式

```bash
# 提取 dashboard JSON 并验证格式
helm template test-addons ./manifests/charts/ai-gateway-addons-helm | \
  yq eval '.data."ai-gateway-dashboard.json"' | \
  jq '.' > dashboard.json

# 验证 JSON 格式是否正确
jq empty dashboard.json && echo "Valid JSON" || echo "Invalid JSON"
```

## 故障排除

### 常见问题

1. **ServiceMonitor 没有被发现**
   - 检查 Prometheus Operator 是否正确安装
   - 验证 ServiceMonitor 的 labels 是否匹配 Prometheus 的 serviceMonitorSelector

2. **Grafana Dashboard 没有显示**
   - 确认 Grafana 的 sidecar 配置正确
   - 检查 ConfigMap 是否有正确的 `grafana_dashboard: "1"` label

3. **模板渲染错误**
   - 使用 `helm template --debug` 查看详细错误信息
   - 检查 values.yaml 中的配置是否正确

### 调试命令

```bash
# 查看 Helm release 详情
helm get all ai-gateway-addons -n monitoring

# 查看 Kubernetes 事件
kubectl get events -n monitoring --sort-by='.lastTimestamp'

# 查看 Pod 日志（如果有相关 Pod）
kubectl logs -n monitoring -l app.kubernetes.io/name=ai-gateway-addons-helm
```

这个测试指南应该能帮助你全面测试 AI Gateway Addons Chart 的功能。
